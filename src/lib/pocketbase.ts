import PocketBase from 'pocketbase';
import type { VenueFormData } from '../types/venue.ts';

// Get PocketBase URL from environment variables
// Use PUBLIC_ prefixed variable for client-side, fallback to server-side variable
const POCKETBASE_URL = import.meta.env.PUBLIC_POCKETBASE_URL || import.meta.env.POCKETBASE_URL || 'https://trodoorentals.pockethost.io';

// console.log('PocketBase URL:', POCKETBASE_URL);

// Create singleton PocketBase client instance
let _pb: PocketBase | null = null;

export function getPocketBase(): PocketBase {
  if (!_pb) {
    _pb = new PocketBase(POCKETBASE_URL);
    
    // Enable auto cancellation for duplicate requests
    _pb.autoCancellation(false);

    // Configure default settings
    _pb.beforeSend = function (url, options) {
      // Add any default headers or configurations here
      return { url, options };
    };
  }

  return _pb;
}

// Export the singleton instance
export const pocketbase = getPocketBase();
export const pb = pocketbase; // Alias for convenience

// Helper functions for common operations
export async function authenticateUser(email: string, password: string) {
  try {
    console.log('Attempting to authenticate user:', email);
    const authData = await pocketbase.collection('users').authWithPassword(email, password);
    console.log('Authentication successful:', authData.record.id);
    return { success: true, user: authData.record, token: authData.token };
  } catch (error) {
    console.error('Authentication failed:', error);

    // Provide more specific error messages
    let errorMessage = 'Authentication failed';
    if (error instanceof Error) {
      if (error.message.includes('Failed to authenticate')) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      } else if (error.message.includes('verification')) {
        errorMessage = 'Please verify your email address before signing in.';
      } else {
        errorMessage = error.message;
      }
    }

    return { success: false, error: errorMessage };
  }
}

export async function registerUser(email: string, password: string, passwordConfirm: string, name?: string) {
  try {
    const userData = {
      email,
      password,
      passwordConfirm,
      name: name || email.split('@')[0], // Use email prefix as default name
    };

    console.log('Attempting to register user:', { email, name: userData.name });

    const user = await pocketbase.collection('users').create(userData);
    console.log('User created successfully:', user.id);

    // Send verification email (optional, don't fail if this fails)
    try {
      await pocketbase.collection('users').requestVerification(email);
      console.log('Verification email sent');
    } catch (verificationError) {
      console.warn('Failed to send verification email:', verificationError);
      // Don't fail the registration if verification email fails
    }

    return { success: true, user };
  } catch (error) {
    console.error('Registration failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Registration failed' };
  }
}

export function logout() {
  pocketbase.authStore.clear();
}

export function getCurrentUser() {
  return pocketbase.authStore.model;
}

export function isAuthenticated(): boolean {
  return pocketbase.authStore.isValid;
}

// Venue-related operations
export async function createVenue(venueData: VenueFormData) {
  try {
    console.log('Creating venue:', venueData.title);

    // Create FormData for file uploads
    const formData = new FormData();

    // Add basic venue data
    formData.append('title', venueData.title);
    formData.append('description', venueData.description);
    formData.append('address', JSON.stringify(venueData.address));
    formData.append('capacity', venueData.capacity.toString());
    formData.append('price_per_hour', venueData.price_per_hour.toString());
    formData.append('amenities', JSON.stringify(venueData.amenities));
    formData.append('is_published', venueData.is_published.toString());
    formData.append('owner', pocketbase.authStore.model?.id || '');

    // Add standard photos
    if (venueData.standard_photos && venueData.standard_photos.length > 0) {
      venueData.standard_photos.forEach((file: File) => {
        formData.append('standard_photos', file);
      });
    }

    // Add panoramic photo
    if (venueData.pano_photo) {
      formData.append('pano_photo', venueData.pano_photo);
    }

    // Add rental agreement PDF
    if (venueData.rental_agreement_pdf) {
      formData.append('rental_agreement_pdf', venueData.rental_agreement_pdf);
    }

    const venue = await pocketbase.collection('venues').create(formData);
    console.log('Venue created successfully:', venue.id);

    return { success: true, venue };
  } catch (error) {
    console.error('Failed to create venue:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create venue'
    };
  }
}

export async function updateVenue(venueId: string, venueData: Partial<VenueFormData>) {
  try {
    console.log('Updating venue:', venueId);

    // Create FormData for file uploads
    const formData = new FormData();

    // Add basic venue data (only include fields that are being updated)
    if (venueData.title !== undefined) formData.append('title', venueData.title);
    if (venueData.description !== undefined) formData.append('description', venueData.description);
    if (venueData.address !== undefined) formData.append('address', JSON.stringify(venueData.address));
    if (venueData.capacity !== undefined) formData.append('capacity', venueData.capacity.toString());
    if (venueData.price_per_hour !== undefined) formData.append('price_per_hour', venueData.price_per_hour.toString());
    if (venueData.amenities !== undefined) formData.append('amenities', JSON.stringify(venueData.amenities));
    if (venueData.is_published !== undefined) formData.append('is_published', venueData.is_published.toString());

    // Add new files if provided
    if (venueData.standard_photos && venueData.standard_photos.length > 0) {
      venueData.standard_photos.forEach((file: File) => {
        formData.append('standard_photos', file);
      });
    }

    if (venueData.pano_photo) {
      formData.append('pano_photo', venueData.pano_photo);
    }

    if (venueData.rental_agreement_pdf) {
      formData.append('rental_agreement_pdf', venueData.rental_agreement_pdf);
    }

    const venue = await pocketbase.collection('venues').update(venueId, formData);
    console.log('Venue updated successfully:', venue.id);

    return { success: true, venue };
  } catch (error) {
    console.error('Failed to update venue:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update venue'
    };
  }
}

export async function getVenue(venueId: string, expand?: string) {
  try {
    const options = expand ? { expand } : {};
    const venue = await pocketbase.collection('venues').getOne(venueId, options);
    return { success: true, venue };
  } catch (error) {
    console.error('Failed to get venue:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get venue'
    };
  }
}

export async function getVenues(page = 1, perPage = 20, filter?: string, sort?: string) {
  try {
    const options: Record<string, unknown> = { page, perPage };
    if (filter) options.filter = filter;
    if (sort) options.sort = sort;

    const result = await pocketbase.collection('venues').getList(page, perPage, options);
    return { success: true, venues: result.items, totalPages: result.totalPages, totalItems: result.totalItems };
  } catch (error) {
    console.error('Failed to get venues:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get venues',
      venues: [],
      totalPages: 0,
      totalItems: 0
    };
  }
}

export async function getUserVenues(userId?: string, page = 1, perPage = 20) {
  try {
    const ownerId = userId || pocketbase.authStore.model?.id;
    if (!ownerId) {
      return { success: false, error: 'User not authenticated', venues: [] };
    }

    const filter = `owner = "${ownerId}"`;
    const result = await pocketbase.collection('venues').getList(page, perPage, {
      filter,
      sort: '-created',
      expand: 'owner'
    });

    return { success: true, venues: result.items, totalPages: result.totalPages, totalItems: result.totalItems };
  } catch (error) {
    console.error('Failed to get user venues:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user venues',
      venues: [],
      totalPages: 0,
      totalItems: 0
    };
  }
}

export async function deleteVenue(venueId: string) {
  try {
    await pocketbase.collection('venues').delete(venueId);
    console.log('Venue deleted successfully:', venueId);
    return { success: true };
  } catch (error) {
    console.error('Failed to delete venue:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete venue'
    };
  }
}

export async function toggleVenuePublished(venueId: string, isPublished: boolean) {
  try {
    const venue = await pocketbase.collection('venues').update(venueId, {
      is_published: isPublished
    });
    console.log('Venue publication status updated:', venueId, isPublished);
    return { success: true, venue };
  } catch (error) {
    console.error('Failed to update venue publication status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update venue publication status'
    };
  }
}

// Booking-related operations
export async function createBooking(bookingData: {
  venue: string;
  start_date: string;
  end_date: string;
  total_price: number;
  platform_fee: number;
  special_requests?: string;
}) {
  try {
    const booking = await pocketbase.collection('bookings').create({
      ...bookingData,
      renter: pocketbase.authStore.model?.id,
      status: 'pending'
    });
    console.log('Booking created successfully:', booking.id);
    return { success: true, booking };
  } catch (error) {
    console.error('Failed to create booking:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create booking'
    };
  }
}

export async function getUserBookings(userId?: string, page = 1, perPage = 20) {
  try {
    const renterId = userId || pocketbase.authStore.model?.id;
    if (!renterId) {
      return { success: false, error: 'User not authenticated', bookings: [] };
    }

    const filter = `renter = "${renterId}"`;
    const result = await pocketbase.collection('bookings').getList(page, perPage, {
      filter,
      sort: '-created',
      expand: 'venue,venue.owner,renter'
    });

    return { success: true, bookings: result.items, totalPages: result.totalPages, totalItems: result.totalItems };
  } catch (error) {
    console.error('Failed to get user bookings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user bookings',
      bookings: [],
      totalPages: 0,
      totalItems: 0
    };
  }
}

// Export types for TypeScript support
export type { RecordModel } from 'pocketbase';
export default pocketbase;
